"use client";

import React, { useState, useEffect, forwardRef } from "react";
import { X } from "lucide-react";

let bannerHeight: number = 0;

export default function Banner() {
  const [bannerOpen, setBannerOpen] = useState(true);
  const [isAtTop, setIsAtTop] = useState(true);

  // Handle scroll to show/hide banner based on scroll position
  useEffect(() => {
    bannerHeight = document.getElementById("banner")?.offsetHeight || 0;
    const [lastScrollY, setLastScrollY] = useState(0); //default window scroll position

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY < bannerHeight) {
        setIsAtTop(true);
        console.log(isAtTop);
      } else {
        setIsAtTop(false);
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  // Don't render if banner is closed
  if (!bannerOpen) return null;

  return (
    <div
      id="banner"
      className={`bg-banner flex justify-center  transition-height duration-300 ease-in-out  ${
        isAtTop ? `h-12` : "h-0"
      }`}
      role="alert"
    >
      <div className="flex justify-between max-w-[var(--max-width)] mx-auto w-full items-center px-3">
        <p className="text-primary-white px-3 grow">Banner Bar Text Here</p>
        <button onClick={() => setBannerOpen(false)}>
          <X className="text-primary-white" size={32} />
        </button>
      </div>
    </div>
  );
}
