"use client";

import React, { useState, useEffect, forwardRef } from "react";
import { X } from "lucide-react";

export default function Banner() {
  const [bannerOpen, setBannerOpen] = useState(true);
  const [isAtTop, setIsAtTop] = useState(true);

  // Handle scroll to show/hide banner based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      setIsAtTop(scrollY < 10); // Show banner only when very close to top
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Don't render if banner is closed or not at top
  if (!bannerOpen || !isAtTop) {
    return null;
  }

  return (
    <div
      id="banner"
      className="bg-banner flex justify-center h-12 transition-all duration-300 ease-in-out"
      role="alert"
    >
      <div className="flex justify-between max-w-[var(--max-width)] mx-auto w-full items-center px-3">
        <p className="text-primary-white px-3 grow">Banner Bar Text Here</p>
        <button onClick={() => setBannerOpen(false)}>
          <X className="text-primary-white" size={32} />
        </button>
      </div>
    </div>
  );
}
