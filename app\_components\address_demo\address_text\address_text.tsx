"use client";

import React, { useState, useEffect } from "react";
import AddressBar from "../address_bar/address_bar";

export default function AddressText() {
  const fullText = "123 Main St, Anytown, USA";
  const [displayedText, setDisplayedText] = useState("");
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const scrollDelta = currentScrollY - lastScrollY;

      // Calculate how many characters to show based on scroll position
      // Adjust the divisor (20) to control sensitivity
      const scrollProgress = Math.max(
        0,
        Math.min(currentScrollY / 20, fullText.length)
      );
      const charactersToShow = Math.floor(scrollProgress);

      // Update displayed text based on scroll direction
      if (scrollDelta > 0) {
        // Scrolling down - add characters
        setDisplayedText(fullText.substring(0, charactersToShow));
      } else if (scrollDelta < 0) {
        // Scrolling up - remove characters
        setDisplayedText(fullText.substring(0, charactersToShow));
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll);

    // Initialize with first character
    setDisplayedText(fullText.substring(0, 1));

    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY, fullText]);

  return (
    <div className="flex items-center fixed">
      <p className="text-6xl font-bold">
        {displayedText}
        <span className="opacity-30">
          {fullText.substring(displayedText.length)}
        </span>
      </p>
      <AddressBar />
    </div>
  );
}
