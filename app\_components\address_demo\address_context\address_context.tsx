"use client";

import React, { useState, useContext, useEffect } from "react";

type AddressContextProps = {
  children: React.ReactNode;
};

type AddressContextType = {
  displayedText: string | null;
  handleScroll: () => void;
};

//full text to be displayed for address
const fullText = "552 Orchard D";

// Create context for address text
export const AddressContext = React.createContext<AddressContextType>({
  displayedText: null,
  handleScroll: () => {},
});

export default function AddressContainer({ children }: AddressContextProps) {
  const [displayedText, setDisplayedText] = useState("");

  const handleScroll = () => {
    const addressSection = document.getElementById("address-demo");
    if (!addressSection) return;
    const sectionRect = addressSection.getBoundingClientRect();
    const sectionHeight = addressSection.offsetHeight;
    const start = 0; // sectionRect.top === 0
    const end = sectionHeight * 0.2; // 10% of section height
    const scrollProgress = Math.max(0, Math.min(-sectionRect.top, end));
    const percentScrolled = scrollProgress / end;

    const charactersToShow = Math.floor(percentScrolled * fullText.length);

    setDisplayedText(fullText.substring(0, charactersToShow));
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <AddressContext.Provider value={{ displayedText, handleScroll }}>
      {children}
    </AddressContext.Provider>
  );
}
