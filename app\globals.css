@import "tailwindcss";

:root {
  --background: #f6f7fb;
  --foreground: #141c23;
  --max-width: 1312px;
  --primary-blue: #06f;
  --secondary-foreground: #49505b;
  --secondary-blue: #141b23;
  --banner: #001d79;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-secondary-foreground: var(--secondary-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-inter: var(--font-inter);
  --font-montserrat: var(--font-montserrat);
  --color-primary-blue: var(--primary-blue);
  --color-primary-white: var(--background);
  --color-secondary-blue: var(--secondary-blue);
  --color-banner: var(--banner);
}
/* Dark Mode Color could add later
@media (prefers-color-scheme: dark) {
  :root {
   
  }
}
*/

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter);
  /* Add Flex Layout  for dynamic layout*/
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main Content Flex Layout*/
main {
  min-height: 150vh;
  flex: 1 0 auto;
}

/* Header Font Styling */
.header {
  font-family: var(--font-montserrat);
  font-weight: 500;
}

.swoop-up {
  opacity: 0;
}

/* Animation */
.swoop-up.in-view {
  animation: swoop-up 0.5s ease-out;
  opacity: 1;
}

@keyframes swoop-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Bolt Left Animation */
.bolt-left {
  opacity: 0;
  transform: translateX(100vw);
}

.bolt-left.in-view {
  animation: bolt-left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 1;
  transform: translateX(0);
}

@keyframes bolt-left {
  0% {
    opacity: 0;
    transform: translateX(100vw);
  }
  60% {
    opacity: 1;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
