@import "tailwindcss";

:root {
  --background: #f6f7fb;
  --foreground: #141c23;
  --max-width: 1312px;
  --primary-blue: #06f;
  --light-primary-blue: rgb(109, 168, 255);
  --secondary-foreground: #49505b;
  --secondary-blue: #141b23;
  --banner: #001d79;
  --pink: #f55591;
  --yellow: #ffbc98;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-light-primary-blue: var(--light-primary-blue);
  --color-secondary-foreground: var(--secondary-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-inter: var(--font-inter);
  --font-montserrat: var(--font-montserrat);
  --color-primary-blue: var(--primary-blue);
  --color-primary-white: var(--background);
  --color-secondary-blue: var(--secondary-blue);
  --color-banner: var(--banner);
  --color-pink: var(--pink);
  --color-yellow: var(--yellow);
  --animate-typer: typer 1s ease-in-out infinite;

  @keyframes typer {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }
}
/* Dark Mode Color could add later
@media (prefers-color-scheme: dark) {
  :root {
   
  }
}
*/

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter);
  /* Add Flex Layout  for dynamic layout*/
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main Content Flex Layout*/
main {
  min-height: 150vh;
  flex: 1 0 auto;
}

/* Header Font Styling */
.header {
  font-family: var(--font-montserrat);
  font-weight: 500;
}

.swoop-up {
  opacity: 0;
}

/* Animation */
.swoop-up.in-view {
  animation: swoop-up 0.3s cubic-bezier(0, 0.25, 0.75, 0.5);
  opacity: 1;
}

@keyframes swoop-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Define the keyframes for the fade-in animation */
@keyframes slideLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}

.slide-left {
  animation: slideLeft infinite linear;
  animation-duration: 60000ms;
}
