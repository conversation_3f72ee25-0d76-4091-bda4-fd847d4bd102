@import "tailwindcss";

:root {
  --background: #f6f7fb;
  --foreground: #141c23;
  --max-width: 1312px;
  --primary-blue: #06f;
  --secondary-foreground: #49505b;
  --secondary-blue: #141b23;
  --banner: #001d79;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-secondary-foreground: var(--secondary-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-inter: var(--font-inter);
  --font-montserrat: var(--font-montserrat);
  --color-primary-blue: var(--primary-blue);
  --color-primary-white: var(--background);
  --color-secondary-blue: var(--secondary-blue);
  --color-banner: var(--banner);
}
/* Dark Mode Color could add later
@media (prefers-color-scheme: dark) {
  :root {
   
  }
}
*/

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter);
  /* Add Flex Layout  for dynamic layout*/
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main Content Flex Layout*/
main {
  min-height: 150vh;
  flex: 1 0 auto;
}

/* Header Font Styling */
.header {
  font-family: var(--font-montserrat);
  font-weight: 700;
}

/* Animation */
.swoop-up.in-view {
  animation: swoop-up 0.3s ease-out;
}

@keyframes swoop-up {
  0% {
    transform: translateY(50px);
  }
  100% {
    transform: translateY(0);
  }
}
